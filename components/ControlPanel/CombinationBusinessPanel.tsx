import React, { memo } from 'react';
import type { CellData } from '../../types/grid';

/**
 * 组合业务面板组件 - 重构版
 * 🎯 核心价值：基于统一CellData结构管理组合业务逻辑
 * ⚡ 性能优化：使用memo包装
 * 📊 功能范围：等待CellDataManager实现后重构
 * 🔄 重构状态：清理现有按钮控制逻辑，保留基础框架
 */

interface CombinationBusinessPanelProps {
  className?: string;
}

export const CombinationBusinessPanel = memo<CombinationBusinessPanelProps>(({ className = '' }) => {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="p-4 bg-purple-50 rounded border border-purple-200">
        <div className="text-sm font-medium text-purple-800 mb-2">🚧 重构进行中</div>
        <div className="text-xs text-purple-600 space-y-1">
          <div>• 正在基于统一CellData结构重构</div>
          <div>• 等待CellDataManager实现完成</div>
          <div>• 将提供分组控制和业务逻辑管理</div>
        </div>
      </div>
    </div>
  );
});

CombinationBusinessPanel.displayName = 'CombinationBusinessPanel';

    return (
      <div className="flex border-b border-gray-700 mb-4">
        {tabs.map(({ key, label, count }) => (
          <button
            key={key}
            onClick={() => handleTabChange(key)}
            className={`flex-1 px-3 py-2 text-sm font-medium transition-colors ${
              activeTab === key
                ? 'text-white border-b-2 border-blue-500'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {label}
            {count !== undefined && count > 0 && (
              <span className="ml-1 text-xs bg-blue-600 text-white rounded-full px-1.5 py-0.5">
                {count}
              </span>
            )}
          </button>
        ))}
      </div>
    );
  };

  // 渲染组合模式Tab
  const renderCombinationTab = () => {
    return (
      <div className="space-y-4">
        {/* 当前模式显示 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">当前模式</label>
          <div className="grid grid-cols-2 gap-2">
            {(Object.keys(MODE_TYPES) as (keyof typeof MODE_TYPES)[]).map((modeKey) => (
              <button
                key={modeKey}
                onClick={() => setCurrentMode(modeKey)}
                className={`p-2 text-xs rounded border transition-colors ${
                  currentMode === modeKey
                    ? 'bg-blue-600 text-white border-blue-500'
                    : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                }`}
              >
                {modeKey === 'DEFAULT' && '默认模式'}
                {modeKey === 'PIE_NA' && '撇捺分组'}
                {modeKey === 'ZHU_HENG' && '横竖分组'}
                {modeKey === 'MIXED' && '混合模式'}
              </button>
            ))}
          </div>
        </div>

        {/* 撇捺分组控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            撇捺分组 ({pieNaGroupConfig.displayName})
          </label>
          <div className="text-xs text-gray-400 mb-2">
            包含颜色: {pieNaGroupConfig.colors.map(color => {
              const colorNames = { red: '红', cyan: '青', yellow: '黄', purple: '紫' };
              return colorNames[color as keyof typeof colorNames];
            }).join(', ')}
          </div>
          <div className="grid grid-cols-5 gap-1">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((groupNum) => {
              const isSelected = pieNaGroupConfig.colors.some(color => {
                const colorGroups = selectedGroups[color];
                // 安全检查：确保 colorGroups 是 Set 类型
                if (!colorGroups || !(colorGroups instanceof Set)) {
                  return false;
                }
                return colorGroups.has(groupNum);
              });
              return (
                <button
                  key={groupNum}
                  onClick={() => togglePieNaGroup(groupNum)}
                  className={`p-1 text-xs rounded border transition-colors ${
                    isSelected
                      ? 'bg-red-600 text-white border-red-500'
                      : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {groupNum}
                </button>
              );
            })}
          </div>
        </div>

        {/* 横竖分组控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            横竖分组 ({zhuHengGroupConfig.displayName})
          </label>
          <div className="text-xs text-gray-400 mb-2">
            包含颜色: {zhuHengGroupConfig.colors.map(color => {
              const colorNames = { orange: '橙', green: '绿', blue: '蓝', pink: '粉' };
              return colorNames[color as keyof typeof colorNames];
            }).join(', ')}
          </div>
          <div className="grid grid-cols-4 gap-1">
            {[11, 12, 13, 14, 21, 22, 23, 24, 31, 32, 33, 34, 41, 42, 43, 44].map((groupNum) => {
              const isSelected = zhuHengGroupConfig.colors.some(color => {
                const colorGroups = selectedGroups[color];
                // 安全检查：确保 colorGroups 是 Set 类型
                if (!colorGroups || !(colorGroups instanceof Set)) {
                  return false;
                }
                return colorGroups.has(groupNum);
              });
              return (
                <button
                  key={groupNum}
                  onClick={() => toggleZhuHengGroup(groupNum)}
                  className={`p-1 text-xs rounded border transition-colors ${
                    isSelected
                      ? 'bg-green-600 text-white border-green-500'
                      : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {groupNum}
                </button>
              );
            })}
          </div>
        </div>

        {/* 批量操作 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">批量操作</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={resetAllGroups}
              className="p-2 text-xs rounded border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors"
            >
              重置所有分组
            </button>
            <button
              onClick={() => updateDefaultModeConfig({ showAllColors: !defaultModeConfig.showAllColors })}
              className={`p-2 text-xs rounded border transition-colors ${
                defaultModeConfig.showAllColors
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              显示所有颜色
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 渲染业务管理Tab
  const renderBusinessTab = () => {
    return (
      <div className="space-y-4">
        {/* 当前业务模式 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">业务模式</label>
          <div className="grid grid-cols-3 gap-2">
            {(['default', 'group', 'mixed'] as const).map((mode) => (
              <button
                key={mode}
                onClick={() => switchMode(mode)}
                className={`p-2 text-xs rounded border transition-colors ${
                  currentActiveMode === mode
                    ? 'bg-blue-600 text-white border-blue-500'
                    : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                }`}
              >
                {mode === 'default' && '默认'}
                {mode === 'group' && '分组'}
                {mode === 'mixed' && '混合'}
              </button>
            ))}
          </div>
        </div>

        {/* 点击状态管理（完整版，移植自传统面板） */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            点击状态管理 ({clickedCells.size} 个格子)
          </label>
          <div className="space-y-2">
            {/* 状态显示区域 */}
            <div className="text-xs text-gray-400 bg-gray-800 p-2 rounded max-h-20 overflow-y-auto">
              {clickedCells.size > 0 ? (
                Array.from(clickedCells).slice(0, 10).map(cellKey => (
                  <span key={cellKey} className="inline-block mr-2 px-1 bg-gray-700 rounded">
                    [{cellKey}]
                  </span>
                ))
              ) : (
                <div>无选中格子</div>
              )}
              {clickedCells.size > 10 && (
                <div className="text-gray-500 mt-1">...还有 {clickedCells.size - 10} 个</div>
              )}
            </div>
            
            {/* 操作按钮 */}
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={clearAllClickedCells}
                disabled={clickedCells.size === 0}
                className="p-2 text-xs rounded border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                清除选中
              </button>
              <button
                onClick={() => showToastNotification(`当前选中 ${clickedCells.size} 个格子`, 'info')}
                className="p-2 text-xs rounded border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors"
              >
                状态信息
              </button>
            </div>
            
            {/* 统计信息 */}
            <div className="text-xs text-gray-500 bg-gray-800 p-2 rounded">
              <div>总计: {clickedCells.size} 个格子</div>
              {clickedCells.size > 0 && (
                <div className="mt-1">
                  最近选中: {Array.from(clickedCells).slice(-1)[0]}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 模式激活状态 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">模式激活状态</label>
          <div className="grid grid-cols-2 gap-1 text-xs">
            {Object.entries(modeActivation).map(([color, isActive]) => {
              const colorNames = {
                red: '红', orange: '橙', cyan: '青', yellow: '黄',
                purple: '紫', green: '绿', blue: '蓝', pink: '粉'
              };
              return (
                <div key={color} className={`p-1 rounded ${isActive ? 'bg-green-800' : 'bg-gray-800'}`}>
                  {colorNames[color as keyof typeof colorNames]}: {isActive ? '开启' : '关闭'}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 渲染版本管理Tab
  const renderVersionsTab = () => {
    return (
      <div className="space-y-4">
        {/* 分组模式版本管理 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            分组模式版本 ({Object.keys(groupModeVersions).length})
          </label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <input
                type="text"
                value={newGroupModeVersionName}
                onChange={(e) => setNewVersionName('groupMode', e.target.value)}
                placeholder="输入版本名称"
                className="flex-1 p-2 text-xs bg-gray-800 border border-gray-600 rounded text-white placeholder-gray-400"
              />
              <button
                onClick={() => {
                  if (newGroupModeVersionName.trim()) {
                    saveVersion('groupMode', newGroupModeVersionName, {
                      selectedGroups: Object.fromEntries(
                        Object.entries(selectedGroups).map(([key, value]) => [key, Array.from(value)])
                      ),
                      currentMode
                    });
                    showToastNotification(`分组版本 "${newGroupModeVersionName}" 已保存`, 'success');
                  }
                }}
                disabled={!newGroupModeVersionName.trim()}
                className="px-3 py-2 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                保存
              </button>
            </div>
            <div className="max-h-24 overflow-y-auto space-y-1">
                             {Object.entries(groupModeVersions).map(([versionName, versionData]) => (
                 <div key={versionName} className="flex items-center justify-between bg-gray-800 p-2 rounded">
                   <div className="flex-1">
                     <div className="text-xs text-white">{versionName}</div>
                     <div className="text-xs text-gray-400">
                       {new Date(versionData.timestamp || Date.now()).toLocaleDateString()}
                     </div>
                   </div>
                  <div className="flex gap-1">
                    <button
                      onClick={() => switchToVersion('groupMode', versionName)}
                      className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      加载
                    </button>
                    <button
                      onClick={() => {
                        deleteVersion('groupMode', versionName);
                        showToastNotification(`版本 "${versionName}" 已删除`, 'info');
                      }}
                      className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 混合模式版本管理 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            混合模式版本 ({Object.keys(mixedModeVersions).length})
          </label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMixedModeVersionName}
                onChange={(e) => setNewVersionName('mixedMode', e.target.value)}
                placeholder="输入版本名称"
                className="flex-1 p-2 text-xs bg-gray-800 border border-gray-600 rounded text-white placeholder-gray-400"
              />
              <button
                onClick={() => {
                  if (newMixedModeVersionName.trim()) {
                    saveVersion('mixedMode', newMixedModeVersionName, {
                      currentActiveMode,
                      clickedCells: Array.from(clickedCells)
                    });
                    showToastNotification(`混合版本 "${newMixedModeVersionName}" 已保存`, 'success');
                  }
                }}
                disabled={!newMixedModeVersionName.trim()}
                className="px-3 py-2 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                保存
              </button>
            </div>
            <div className="max-h-24 overflow-y-auto space-y-1">
                             {Object.entries(mixedModeVersions).map(([versionName, versionData]) => (
                 <div key={versionName} className="flex items-center justify-between bg-gray-800 p-2 rounded">
                   <div className="flex-1">
                     <div className="text-xs text-white">{versionName}</div>
                     <div className="text-xs text-gray-400">
                       {new Date(versionData.timestamp || Date.now()).toLocaleDateString()}
                     </div>
                   </div>
                  <div className="flex gap-1">
                    <button
                      onClick={() => switchToVersion('mixedMode', versionName)}
                      className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      加载
                    </button>
                    <button
                      onClick={() => {
                        deleteVersion('mixedMode', versionName);
                        showToastNotification(`版本 "${versionName}" 已删除`, 'info');
                      }}
                      className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Tab导航 */}
      {renderTabNavigation()}

      {/* Tab内容 */}
      <div className="min-h-[400px]">
        {activeTab === 'combination' && renderCombinationTab()}
        {activeTab === 'business' && renderBusinessTab()}
        {activeTab === 'versions' && renderVersionsTab()}
      </div>

      {/* Toast提示 */}
      {toastState.show && (
        <div className={`fixed top-5 right-5 p-3 rounded-md text-white z-50 ${
          toastState.type === 'success' ? 'bg-green-600' : 
          toastState.type === 'error' ? 'bg-red-600' : 'bg-blue-600'
        }`}>
          {toastState.message}
        </div>
      )}
    </div>
  );
});

CombinationBusinessPanel.displayName = 'CombinationBusinessPanel'; 